import React from "react";

interface ButtonProps {
  onClick: (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void;
  disabled?: boolean;
  variant?: "default" | "outline" | "danger";
  children: React.ReactNode;
  className?: string;
  instructions?: string;
}

const Button: React.FC<ButtonProps> = ({
  onClick,
  disabled = false,
  variant = "default",
  children,
  className = "",
  
}) => {
  const baseStyles =
    "px-4 py-2 rounded-md font-medium transition duration-200 ease-in-out";

  const variantStyles = {
    default: "bg-blue-500 text-white hover:bg-blue-600",
    outline: "border border-gray-300 text-gray-700 hover:bg-gray-100",
    danger: "bg-red-500 text-white hover:bg-red-600",
  };

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`${baseStyles} ${variantStyles[variant]} ${
        disabled ? "opacity-50 cursor-not-allowed" : ""
      } ${className}`}
    >
      {children}
    </button>
  );
};

export default Button;
