import { useMutation } from "@tanstack/react-query";
export const useUpdateCandidateAPI = () => {
  return useMutation({
    mutationFn: async (data) => {
      const response = await fetch(
        `${import.meta.env.VITE_API_URL}/manualupload/candidate`,
       
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message || "Failed to update candidate"
        );
      }

      return response.json();
    },
  });
};
